# LangChain FastAPI 项目

## 项目简介

这是一个基于 FastAPI 和 LangChain/LangGraph 的 AI 代理服务项目。该项目提供了一个可扩展的 API 服务，用于构建和部署基于大型语言模型的智能代理。通过结合 FastAPI 的高性能和 LangChain/LangGraph 的强大功能，本项目可以快速构建复杂的 AI 应用。

## 主要特性

- 基于 FastAPI 的高性能 API 服务
- 集成 LangChain 和 LangGraph 框架
- 支持多种大型语言模型（如 OpenAI、Anthropic、Gemini 等）
- 模块化的项目结构，易于扩展
- 完整的日志和异常处理
- 基于 Pydantic 的请求/响应模型验证
- 单元测试支持
- 支持异步处理和流式响应
- 内置中间件和依赖注入系统

## 安装指南

### 前置条件

- Python 3.9+
- pip

### 安装步骤

1. 克隆仓库
   ```bash
   git clone <repository-url>
   cd langchain_fastapi_project
   ```

2. 创建并激活虚拟环境（可选但推荐）
   ```bash
   python -m venv venv
   # Windows
   venv\Scripts\activate
   # Linux/Mac
   source venv/bin/activate
   ```

3. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

4. 配置环境变量
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，设置必要的环境变量
   ```

## 使用方法

### 启动服务

```bash
uvicorn app.main:app --reload
```

服务将在 http://localhost:8000 启动

### API 文档

启动服务后，可以通过以下链接访问自动生成的 API 文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### API 使用示例

#### 使用 LangChain Agent

```python
import requests
import json

url = "http://localhost:8000/api/v1/agent/query"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_API_KEY"
}
payload = {
    "query": "帮我查询今天的天气",
    "tools": ["search", "calculator"],
    "model": "gpt-4"
}

response = requests.post(url, headers=headers, json=payload)
print(json.dumps(response.json(), indent=2, ensure_ascii=False))
```

#### 使用 LangGraph Agent

```python
import requests
import json

url = "http://localhost:8000/api/v1/langgraph/run"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_API_KEY"
}
payload = {
    "input": "分析以下数据并给出结论",
    "data": {"sales": [100, 200, 150, 300]},
    "config": {
        "model": "gpt-4",
        "max_iterations": 5
    }
}

response = requests.post(url, headers=headers, json=payload)
print(json.dumps(response.json(), indent=2, ensure_ascii=False))
```

### 环境变量配置

项目使用 `.env` 文件管理环境变量。以下是主要的环境变量：

```
# API 配置
API_VERSION=v1
API_PREFIX=/api
DEBUG=True

# 安全配置
SECRET_KEY=your_secret_key_here
API_KEY=your_api_key_here
ALLOWED_HOSTS=localhost,127.0.0.1

# LLM 配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log
```

## 项目结构

```
langchain_fastapi_project/
├── app/
│   ├── __init__.py
│   ├── main.py                     # FastAPI 应用入口
│   ├── api/
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── endpoints/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── agent.py        # LangChain-Agent 相关路由
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py               # 环境变量和配置
│   │   ├── logging.py              # 日志配置
│   │   ├── dependencies.py         # 依赖注入
│   ├── LangGraph/
│   │   ├── __init__.py
│   │   ├── agent.py                # LangGraph-Agent 逻辑
│   ├── models/
│   │   ├── __init__.py
│   │   ├── request.py              # 请求模型（Pydantic）
│   │   ├── response.py             # 响应模型（Pydantic）
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── exceptions.py           # 自定义异常
├── tests/
│   ├── __init__.py
│   ├── test_agent.py               # 单元测试
├── .env                            # 环境变量文件
├── .gitignore                      # Git 忽略文件
├── requirements.txt                # 依赖文件
├── README.md                       # 项目说明
├── pyproject.toml                  # 包管理配置文件（可选）
```

## 技术架构

### 技术栈

- **Web 框架**: FastAPI
- **LLM 框架**: LangChain, LangGraph
- **数据验证**: Pydantic
- **异步处理**: asyncio
- **日志管理**: loguru
- **测试框架**: pytest
- **文档**: Swagger/OpenAPI

### 架构设计

本项目采用分层架构设计：

1. **API 层**: 处理 HTTP 请求和响应
2. **服务层**: 实现业务逻辑
3. **模型层**: 定义数据结构
4. **工具层**: 提供通用功能

### 数据流

```
客户端请求 → FastAPI 路由 → 依赖注入 → 服务处理 → LangChain/LangGraph 处理 → 响应
```

## 开发指南

### 添加新的端点

1. 在 `app/api/v1/endpoints/` 目录下创建新的路由文件
2. 在 `app/models/` 目录下定义请求和响应模型
3. 在 `app/main.py` 中注册新的路由

### 添加新的 LLM 工具

1. 在 `app/LangGraph/tools/` 目录下创建新的工具类
2. 实现 `BaseTool` 接口
3. 在 `app/LangGraph/agent.py` 中注册新工具

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_agent.py

# 运行带标记的测试
pytest -m "integration"
```

### 代码风格检查

```bash
# 运行代码风格检查
flake8 app tests

# 自动格式化代码
black app tests
```

## 贡献指南

1. Fork 该仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开一个 Pull Request

## 许可证

[MIT](LICENSE)

## 常见问题解答 (FAQ)

### Q: 如何选择合适的 LLM 模型？

A: 根据您的需求选择合适的模型：
- 对于复杂推理任务，推荐使用 GPT-4 或 Claude 3
- 对于简单任务，可以使用 GPT-3.5-Turbo 或 Gemini Pro
- 如果需要处理多模态内容，可以使用 GPT-4V 或 Gemini Pro Vision

### Q: 如何处理 API 密钥安全问题？

A: 我们建议：
1. 永远不要在代码中硬编码 API 密钥
2. 使用环境变量或安全的密钥管理服务
3. 实施 API 密钥轮换策略
4. 使用 HTTPS 加密所有通信

### Q: 如何优化 LLM 调用成本？

A: 可以通过以下方式优化成本：
1. 使用缓存避免重复查询
2. 实施批处理请求
3. 使用嵌入和向量数据库减少 LLM 调用
4. 为不同任务选择合适的模型

### Q: 如何扩展系统以处理高并发请求？

A: 可以考虑：
1. 使用异步处理
2. 实施队列系统
3. 水平扩展 API 服务
4. 使用负载均衡器

## 联系方式

如有任何问题或建议，请通过以下方式联系我们：

- 项目维护者: [维护者姓名](mailto:<EMAIL>)
- 项目仓库: [GitHub 仓库链接](https://github.com/username/langchain_fastapi_project)
- 技术支持: [支持邮箱](mailto:<EMAIL>)