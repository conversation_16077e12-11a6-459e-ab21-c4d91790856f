from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
import logging as log
import json
from typing import AsyncGenerator, Dict, Any
# 使用更新后的包结构导入
from app.models import AgentRequest, AgentResponse
from app.langraph import HumanMessage
from app.core.dependencies import get_agent

router = APIRouter()

async def stream_generator(request: AgentRequest, graph) -> AsyncGenerator[str, None]:
    """
    生成SSE流式响应的生成器函数

    Args:
        request: 用户请求
        graph: LangGraph实例

    Yields:
        SSE格式的事件数据
    """
    log.info("执行流式任务：{}".format(request.query))
    initial_state = {"messages": [HumanMessage(content=request.query)]}

    try:
        # 发送开始事件
        yield format_sse_event(
            event="start",
            data={"type": "start", "content": "开始处理请求..."}
        )

        # 使用LangGraph的stream方法，设置流式模式为"messages"
        # 这将按token流式输出LLM生成的内容
        async for chunk in graph.astream(initial_state, stream_mode="messages"):
            # 如果有消息内容，发送内容事件
            if "messages" in chunk and len(chunk["messages"]) > 0:
                message = chunk["messages"][-1]
                if hasattr(message, "content") and message.content:
                    yield format_sse_event(
                        event="content",
                        data={"type": "content", "content": message.content}
                    )

        # 获取最终结果
        final_result = graph.invoke(initial_state)
        final_content = final_result["messages"][-1].content if "messages" in final_result and len(final_result["messages"]) > 0 else ""

        # 发送完成事件
        yield format_sse_event(
            event="end",
            data={"type": "end", "content": final_content}
        )

    except Exception as e:
        log.error(f"流式处理出错: {str(e)}")
        # 发送错误事件
        yield format_sse_event(
            event="error",
            data={"type": "error", "content": f"处理请求时出错: {str(e)}"}
        )
        # 不抛出异常，让生成器正常结束

def format_sse_event(event: str, data: Dict[str, Any]) -> str:
    """
    格式化SSE事件

    Args:
        event: 事件类型
        data: 事件数据

    Returns:
        格式化的SSE事件字符串
    """
    json_data = json.dumps(data, ensure_ascii=False)
    return f"event: {event}\ndata: {json_data}\n\n"

@router.post("/stream")
async def stream_agent_post(request: AgentRequest, graph=Depends(get_agent)):
    """
    流式响应接口 (POST)，使用SSE (Server-Sent Events) 技术
    返回处理过程中的实时更新

    客户端可以使用标准的EventSource API接收流式响应
    """
    return StreamingResponse(
        stream_generator(request, graph),
        media_type="text/event-stream"
    )

@router.get("/stream")
async def stream_agent_get(query: str, graph=Depends(get_agent)):
    """
    流式响应接口 (GET)，使用SSE (Server-Sent Events) 技术
    返回处理过程中的实时更新

    客户端可以使用标准的EventSource API接收流式响应

    注意：EventSource默认只支持GET请求，所以提供这个端点方便前端直接使用EventSource
    """
    request = AgentRequest(query=query)
    return StreamingResponse(
        stream_generator(request, graph),
        media_type="text/event-stream"
    )
