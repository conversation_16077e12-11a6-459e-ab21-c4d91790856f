"""
可视化相关的 API 端点
"""
from fastapi import APIRouter, HTTPException
from fastapi.responses import Response
import logging as log

# 使用更新后的包结构导入
from app.langraph import visualize_graph

router = APIRouter()

@router.get("/graph", response_class=Response)
async def get_graph_visualization():
    """
    获取 LangGraph 图表的可视化图像

    Returns:
        PNG 格式的图表图像
    """
    try:
        # 获取图表的二进制数据
        png_data = visualize_graph(output_format="bytes")

        if png_data is None:
            raise HTTPException(status_code=500, detail="无法生成图表")

        # 返回 PNG 图像
        return Response(content=png_data, media_type="image/png")
    except Exception as e:
        log.error(f"生成图表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成图表时出错: {str(e)}")
