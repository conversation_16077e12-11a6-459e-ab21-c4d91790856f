import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from app.models.agent_enum import AllModelEnum

load_dotenv()
class Settings(BaseSettings):
    PROJECT_NAME: str = "langgraph FastAPI"
    API_V1_STR: str = "/api/v1"
    API_THREAD_STR: str = "/threads"
    ENVIRONMENT: str = "development"
    MODEL_NAME: str = "qwen3"
    QWEN_API_KEY: str | None = os.getenv("QWEN_API_KEY")
    QWEN_API_URL: str | None = None
    LOG_LEVEL: str = "INFO"
    DEFAULT_MODEL: AllModelEnum | None = None  # type: ignore[assignment]

    class Config:
        env_file = "../../.env"
        env_file_encoding = "utf-8"
        case_sensitive = True

settings = Settings()

