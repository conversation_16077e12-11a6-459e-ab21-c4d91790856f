from langgraph.graph import StateGraph, END, START
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, AIMessage
from openai import OpenAI
from typing import TypedDict, List, Optional
from app.core.config import settings
import logging as log
# 定义状态
class State(TypedDict):
    # 消息列表，包含对话历史
    messages: List[HumanMessage | AIMessage]

# 初始化 OpenAI 模型客户端（如果配置了 API 密钥）
client = None
log.info("加载配置: QWEN_API_KEY={}, QWEN_API_URL={}".format(settings.QWEN_API_KEY, settings.QWEN_API_URL))
if settings.QWEN_API_KEY:
    try:
        client = OpenAI(
            base_url=settings.QWEN_API_URL,
            api_key=settings.QWEN_API_KEY
        )
    except Exception as e:
        log.warning(f"初始化 OpenAI 客户端失败: {str(e)}")

# 定义提示模板
prompt = ChatPromptTemplate.from_template("你是一个智能助手，回答用户问题：{input}")

# 定义调用 LLM 的节点
def call_llm(state: State) -> State:
    user_input = state["messages"][-1].content
    # 如果客户端已初始化，则调用 API
    if client:
        try:
            completion = client.chat.completions.create(
                model=settings.MODEL_NAME,
                messages=[{"role": "user", "content": prompt.format(input=user_input)}],
                stream=True,
                extra_body={"enable_thinking": False}
            )
            full_content = ""
            print(f"开始流式输出:")
            for chunk in completion:
                # 如果stream_options.include_usage为True，则最后一个chunk的choices字段为空列表，需要跳过（可以通过chunk.usage获取 Token 使用量）
                if chunk.choices:
                    chunk_content = chunk.choices[0].delta.content
                    full_content += chunk_content or ""
                    print(chunk_content)
            state["messages"].append(AIMessage(content=full_content or ""))
        except Exception as e:
            log.error(f"使用配置的模型: {settings.MODEL_NAME}, 模型apiKey{settings.QWEN_API_KEY}, 模型地址{settings.QWEN_API_URL}")
            log.error(f"调用 LLM API 失败: {str(e)}")
            state["messages"].append(AIMessage(content="抱歉，我无法处理您的请求。"))
    else:
        # 如果客户端未初始化，返回默认消息
        state["messages"].append(AIMessage(content="模型未初始化，无法处理请求。"))

    return state

# 创建 LangGraph 工作流
def create_graph():
    log.info("创建 LangGraph agent")
    graph_builder = StateGraph(State)

    # 添加 LLM 节点
    graph_builder.add_node("llm", call_llm)

    # 设置边缘连接
    graph_builder.add_edge(START, "llm")
    graph_builder.add_edge("llm", END)

    # 编译图
    graph = graph_builder.compile()
    return graph

# 初始化图
graph = create_graph()


# 执行 LangGraph
def run_agent(user_input: str) -> str:
    initial_state = {"messages": [HumanMessage(content=user_input)]}
    result = graph.invoke(initial_state)
    return result["messages"][-1].content


# 可视化 LangGraph
def visualize_graph(output_format: str = "display", save_path: Optional[str] = None) -> Optional[bytes]:
    """
    生成并可视化 LangGraph 图表

    Args:
        output_format: 输出格式，可选值为 "display"（直接显示）或 "bytes"（返回图像字节）
        save_path: 可选的保存路径，如果提供则将图表保存到该路径

    Returns:
        如果 output_format 为 "bytes"，则返回 PNG 图像的字节数据；否则返回 None
    """
    from app.utils.visualization import try_display_graph

    # 使用通用的可视化工具函数
    png_data = try_display_graph(graph.get_graph(), save_path)

    if output_format == "bytes" and png_data is not None:
        return png_data
    return None
