from dataclasses import Field
from typing import Dict
from fastapi import Body, Depends, FastAPI, HTTPException
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
from app.models.agent_enum import OpenAICompatibleName
from app.schema.models import ServiceMetadata
from app.schema.agents import get_all_agent_info, DEFAULT_AGENT
from app.core import settings, configure_logging
from app.api.v1.endpoints import agent, thread, visualization
import asyncio
import json
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_openai import ChatOpenAI
from app.core.dependencies import get_agent
from app.models.custom_request import LangGraphRoute
logger = configure_logging(settings.LOG_LEVEL)

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Application startup")
    yield
    logger.info("Application shutdown")

app = FastAPI(
    title=settings.PROJECT_NAME,
    lifespan=lifespan
)

# 配置CORS中间件，允许所有域名访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有域名访问
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有HTTP头
)

# 注册路由
app.include_router(agent.router, prefix=f"{settings.API_V1_STR}/agent")
app.include_router(visualization.router, prefix=f"{settings.API_V1_STR}/visualization")
app.include_router(
    thread.router,
    prefix=f"{settings.API_THREAD_STR}"
)

@app.get("/")
async def root():
    return {"message": "Welcome to jk agent"}


@app.get("/info")
async def info() -> ServiceMetadata:
    if settings.DEFAULT_MODEL is None:
        models = []
    elif isinstance(settings.DEFAULT_MODEL, str):
        models = [settings.DEFAULT_MODEL]
    else:
        models = list(settings.DEFAULT_MODEL)
    models.sort()
    return ServiceMetadata(
        agents=get_all_agent_info(),
        models=models,
        default_agent=DEFAULT_AGENT,
        default_model=settings.DEFAULT_MODEL or OpenAICompatibleName.OPENAI_COMPATIBLE,
    )

# 内存存储线程数据（生产环境应使用数据库）
threads_db = {}

# 消息处理接口
@app.post("/runs/stream")
async def create_run(thread_id: str, data: Dict = Body(...), graph=Depends(get_agent)):
    """创建并执行运行"""
    if thread_id not in threads_db:
        raise HTTPException(status_code=404, detail="Thread not found")
    
    # 获取线程数据
    thread = threads_db[thread_id]
    
    # 处理输入消息
    messages = data.get("messages", [])
    context = data.get("context")
    
    # 转换消息格式
    langchain_messages = []
    for msg in messages:
        if msg["type"] == "human":
            content = ""
            if isinstance(msg["content"], list):
                for item in msg["content"]:
                    if item["type"] == "text":
                        content += item["text"]
            else:
                content = msg["content"]
            langchain_messages.append(HumanMessage(content=content))
        elif msg["type"] == "ai":
            langchain_messages.append(AIMessage(content=msg["content"]))
    
    # 准备输入状态
    input_state = {
        "messages": langchain_messages,
        "context": context
    }
    
    # 创建流式响应
    async def stream_response():
        # 执行图
        config = {"recursion_limit": 25}
        stream = graph.stream(input_state, config=config)
        
        # 流式返回结果
        try:
            async for event in stream:
                if "messages" in event:
                    # 格式化输出
                    output = {
                        "messages": [
                            {
                                "id": str(uuid4()),
                                "type": "ai" if isinstance(msg, AIMessage) else "human",
                                "content": msg.content
                            } for msg in event["messages"]
                        ]
                    }
                    
                    # 更新线程数据
                    thread["messages"] = output["messages"]
                    
                    # 返回事件
                    yield f"data: {json.dumps(output)}\n\n"
                    
            # 发送完成事件
            yield f"data: [DONE]\n\n"
        except Exception as e:
            error_data = {"error": str(e)}
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        stream_response(),
        media_type="text/event-stream"
    )