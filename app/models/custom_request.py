import json
from typing import Callable, Dict, Any
from fastapi import Request, Response
from fastapi.routing import APIRoute

class LangGraphRequest(Request):
    """处理LangGraph请求的自定义请求类"""
    
    async def body(self) -> bytes:
        if not hasattr(self, "_body"):
            body = await super().body()
            self._body = body
        return self._body
    
    async def json(self) -> Dict[str, Any]:
        """解析JSON请求体"""
        body = await self.body()
        return json.loads(body)

class LangGraphRoute(APIRoute):
    """自定义路由类，用于处理LangGraph请求"""
    
    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()
        
        async def custom_route_handler(request: Request) -> Response:
            request = LangGraphRequest(request.scope, request.receive)
            return await original_route_handler(request)
            
        return custom_route_handler