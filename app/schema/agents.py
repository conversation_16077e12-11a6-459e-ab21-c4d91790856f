from dataclasses import dataclass
from langgraph.pregel import <PERSON>gel
from app.schema.models import AgentInfo
from app.langraph.agent import create_graph

DEFAULT_AGENT = "research-assistant"




def get_agent(agent_id: str) -> Pregel:
    return create_graph()


def get_all_agent_info() -> list[AgentInfo]:
    return [
        AgentInfo(key=agent_id, description=agent.description) for agent_id, agent in list()
    ]
