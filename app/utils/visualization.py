"""
可视化工具模块，提供图表生成和显示功能
"""
import logging as log
from typing import Optional, Union, Dict, Any
import os

def save_graph_image(graph_data: bytes, file_path: str) -> bool:
    """
    将图表数据保存为图像文件
    
    Args:
        graph_data: 图表的二进制数据
        file_path: 保存路径
        
    Returns:
        bool: 保存是否成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(graph_data)
        
        log.info(f"图表已保存至: {file_path}")
        return True
    except Exception as e:
        log.error(f"保存图表时出错: {str(e)}")
        return False

def try_display_graph(graph_obj: Any, save_path: Optional[str] = None) -> Optional[bytes]:
    """
    尝试显示或保存图表
    
    Args:
        graph_obj: 图表对象，必须有 draw_mermaid_png 方法
        save_path: 可选的保存路径
        
    Returns:
        Optional[bytes]: 如果成功生成图表，返回图表的二进制数据；否则返回 None
    """
    try:
        # 尝试导入 IPython 相关模块
        from IPython.display import Image, display
        
        # 生成图表数据
        png_data = graph_obj.draw_mermaid_png()
        
        # 如果在 Jupyter 环境中，显示图表
        try:
            display(Image(png_data))
            log.info("图表已在 Jupyter 环境中显示")
        except Exception:
            log.info("不在 Jupyter 环境中，无法直接显示图表")
        
        # 如果提供了保存路径，保存图表
        if save_path:
            save_graph_image(png_data, save_path)
        
        return png_data
    except ImportError:
        log.warning("无法导入 IPython 模块，请确保已安装相关依赖")
        return None
    except Exception as e:
        log.warning(f"生成或显示图表时出错: {str(e)}")
        return None
