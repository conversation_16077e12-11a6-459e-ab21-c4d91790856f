{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LangGraph 可视化示例\n", "\n", "本笔记本演示如何在 Jupyter 环境中可视化 LangGraph 图表。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import sys\n", "\n", "# 添加项目根目录到 Python 路径\n", "sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath('.'))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 导入必要的模块\n", "from app.langraph.agent import graph, visualize_graph"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 方法 1: 直接显示图表\n", "\n", "最简单的方法是直接调用 `visualize_graph()` 函数，它会在 Jupyter 笔记本中显示图表。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 直接显示图表\n", "visualize_graph()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 方法 2: 使用 IPython 的 display 函数\n", "\n", "另一种方法是使用 IPython 的 display 函数直接显示图表。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    # 获取图表的 PNG 数据\n", "    png_data = graph.get_graph().draw_mermaid_png()\n", "    \n", "    # 显示图表\n", "    display(Image(png_data))\n", "except Exception as e:\n", "    print(f\"生成图表时出错: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 方法 3: 保存图表到文件\n", "\n", "您还可以将图表保存到文件中，以便在其他地方使用。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 保存图表到文件\n", "output_dir = \"output\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "output_path = os.path.join(output_dir, \"langgraph_visualization.png\")\n", "\n", "visualize_graph(save_path=output_path)\n", "print(f\"图表已保存至: {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}