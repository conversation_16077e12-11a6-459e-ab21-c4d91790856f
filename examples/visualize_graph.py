"""
LangGraph 可视化示例
"""
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.langraph.agent import visualize_graph

def main():
    """
    主函数，演示如何使用可视化功能
    """
    print("正在生成 LangGraph 可视化图表...")
    
    # 方法 1: 在 Jupyter 环境中直接显示
    visualize_graph()
    
    # 方法 2: 保存为文件
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, "langgraph_visualization.png")
    
    visualize_graph(save_path=output_path)
    print(f"图表已保存至: {output_path}")

if __name__ == "__main__":
    main()
